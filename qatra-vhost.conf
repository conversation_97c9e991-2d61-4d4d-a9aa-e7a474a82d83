# Virtual Host Configuration for Qatra Laravel Project
# Add this to /Applications/XAMPP/xamppfiles/etc/extra/httpd-vhosts.conf

<VirtualHost *:80>
    ServerName qatra.local
    ServerAlias www.qatra.local
    DocumentRoot "/Applications/XAMPP/xamppfiles/htdocs/qatra/public"
    
    <Directory "/Applications/XAMPP/xamppfiles/htdocs/qatra/public">
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        
        # Enable URL rewriting
        RewriteEngine On
        
        # Handle Angular and other front-end routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>
    
    # Logging
    ErrorLog "logs/qatra-error.log"
    CustomLog "logs/qatra-access.log" common
    
    # PHP Configuration
    php_value upload_max_filesize 64M
    php_value post_max_size 64M
    php_value max_execution_time 300
    php_value max_input_vars 3000
</VirtualHost>
